<div
    bsModal
    #createOrEditModal="bs-modal"
    class="modal fade"
    tabindex="-1"
    role="dialog"
    aria-labelledby="createOrEditModal"
    aria-hidden="true"
    [config]="{ backdrop: 'static' }"
>
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form *ngIf="active" #orderFullfillmentForm="ngForm" novalidate (ngSubmit)="save()" autocomplete="off">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <span *ngIf="orderFullfillment.id">{{ l('EditOrderFullfillment') }}</span>
                        <span *ngIf="!orderFullfillment.id">{{ l('CreateNewOrderFullfillment') }}</span>
                    </h4>

                    <button
                        type="button"
                        class="btn-close"
                        (click)="close()"
                        aria-label="Close"
                        [disabled]="saving"
                    ></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-8">
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Order Source -->
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label" for="OrderSourceName">
                                                {{ l('OrderSource') }}
                                            </label>
                                            <select
                                                name="orderSourceId"
                                                [(ngModel)]="orderFullfillment.orderSourceId"
                                                class="form-select"
                                                (ngModelChange)="checkAndLoadOrderDetails()"
                                            >
                                                <option value="">{{ l('SelectAOrderSource') }}</option>
                                                <option *ngFor="let item of allOrderSources" [value]="item.id">
                                                    {{ item.displayName }}
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Customer -->
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label" for="CustomerName">{{ l('Customer') }}</label>
                                            <select
                                                name="customerId"
                                                [(ngModel)]="orderFullfillment.customerId"
                                                class="form-select"
                                                (ngModelChange)="checkAndLoadOrderDetails()"
                                            >
                                                <option value="">{{ l('SelectACustomer') }}</option>
                                                <option *ngFor="let item of allCustomers" [value]="item.id">
                                                    {{ item.displayName }}
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Location Picker -->
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label" for="LocationBarangay">{{ l('Location') }}</label>
                                            <div class="input-group">
                                                <input
                                                    class="form-control"
                                                    id="LocationBarangay"
                                                    name="locationBarangay"
                                                    [(ngModel)]="locationBarangay"
                                                    type="text"
                                                    disabled
                                                />
                                                <button
                                                    class="btn btn-primary blue"
                                                    (click)="openSelectLocationModal()"
                                                    type="button"
                                                >
                                                    <i class="fa fa-search"></i>
                                                    {{ l('Pick') }}
                                                </button>
                                                <button
                                                    class="btn btn-danger btn-icon"
                                                    type="button"
                                                    (click)="setLocationIdNull()"
                                                >
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                            <!-- Hidden field -->
                                            <input
                                                class="form-control"
                                                name="orderFullfillment.locationId"
                                                [(ngModel)]="orderFullfillment.locationId"
                                                type="text"
                                                hidden
                                            />
                                        </div>

                                        <!-- Order Placement Clinic Header -->
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label" for="OrderPlacementClinicHeaderDocNumber">
                                                {{ l('OrderSlipDocNumber') }}
                                            </label>
                                            <select
                                                name="orderPlacementClinicHeaderId"
                                                [(ngModel)]="orderFullfillment.orderPlacementClinicHeaderId"
                                                class="form-select"
                                                (ngModelChange)="checkAndLoadOrderDetails()"
                                            >
                                                <option value="">{{ l('SelectAOrderPlacementClinicHeader') }}</option>
                                                <option
                                                    *ngFor="let item of allOrderPlacementClinicHeaders"
                                                    value="{{ item.id }}"
                                                >
                                                    {{ item.displayName }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h5>
                            <strong><em>Order Details</em></strong>
                        </h5>
                        <table class="table table-bordered table-striped align-middle text-center">
                            <thead class="table-light">
                                <tr>
                                    <!-- <th>Actions</th> -->
                                    <th>Products</th>
                                    <th>Product Role</th>
                                    <th>Promotion Name</th>
                                    <th>Quantity</th>
                                    <th>Batch</th>
                                    <th>Expiry Date</th>
                                    <th>Unit Price</th>
                                    <th>Promo Price</th>
                                    <th>Total Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of orderDetails; let i = index">
                                    <!-- Actions -->
                                    <!-- <td>
                                        <button
                                            class="btn btn-sm btn-danger"
                                            type="button"
                                            (click)="removeOrderDetail(i)"
                                        >
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td> -->
                                    <!-- Products -->
                                    <td>{{ item.productName }}</td>
                                    <!-- Product Role -->
                                    <td>{{ item.productRole }}</td>
                                    <!-- Promotion Name -->
                                    <td>{{ item.promotionName }}</td>
                                    <!-- Quantity -->
                                    <td>{{ item.quantity }}</td>
                                    <!-- Batch -->
                                    <td>{{ item.batch }}</td>
                                    <!-- Expiry Date -->
                                    <td>{{ item.expiryDate | date: 'shortDate' }}</td>
                                    <!-- Unit Price -->
                                    <td>{{ item.unitPrice | currency }}</td>
                                    <!-- Promo Price -->
                                    <td>{{ item.promoPrice | currency }}</td>
                                    <!-- Total Price -->
                                    <td>{{ item.totalPrice | currency }}</td>
                                </tr>
                                <tr *ngIf="orderDetails.length === 0">
                                    <td colspan="10" class="text-center text-muted">
                                        <em>{{ l('NoOrderDetailsFound') }}</em>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button [disabled]="saving" type="button" class="btn btn-default" (click)="close()">
                        {{ l('Cancel') }}
                    </button>
                    <button
                        type="submit"
                        class="btn btn-primary blue"
                        [disabled]="saving"
                        [buttonBusy]="saving"
                        [busyText]="l('SavingWithThreeDot')"
                    >
                        <i class="fa fa-save"></i>
                        <span>{{ l('Save') }}</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <orderFullfillmentLocationLookupTableModal
        #orderFullfillmentLocationLookupTableModal
        (modalSave)="getNewLocationId()"
    ></orderFullfillmentLocationLookupTableModal>
</div>
